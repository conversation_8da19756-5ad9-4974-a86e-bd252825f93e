chrome.action.onClicked.addListener((tab) => {
  // Check if we can script on this tab. Don't open a window for chrome:// pages.
  if (tab.url.startsWith('chrome://') || tab.url.startsWith('https://chrome.google.com')) {
    console.log("Cannot script on this page.");
    return;
  }
  
  const width = 1000;
  const height = 800;

  // No need for getCurrent anymore, we can get screen dimensions from the tab's window
  chrome.windows.get(tab.windowId, (win) => {
    const left = Math.round(win.left + (win.width - width) / 2);
    const top = Math.round(win.top + (win.height - height) / 2);

    // Create the window and pass the tab ID in the URL hash
    chrome.windows.create({
      // Add the tabId to the URL hash
      url: chrome.runtime.getURL('index.html#' + tab.id),
      type: 'popup',
      width: width,
      height: height,
      left: left,
      top: top
    });
  });
});