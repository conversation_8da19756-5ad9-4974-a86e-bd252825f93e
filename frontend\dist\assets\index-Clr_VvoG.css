.modal-overlay{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.modal-content{background-color:#fff;padding:20px;border-radius:8px;width:80%;max-width:900px;height:80%;overflow-y:auto;position:relative}.close-button{position:absolute;top:10px;right:10px;background:none;border:none;font-size:1.2em;cursor:pointer}html,body,#root{height:100%;width:100%;margin:0;padding:0;overflow:hidden;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,sans-serif}.App{display:flex;flex-direction:column;height:100vh;width:100vw;background-color:#282c34}.tab-bar{flex-shrink:0;display:flex;background-color:#20232a;border-bottom:1px solid #444}.tab-button{padding:12px 24px;font-size:16px;font-weight:500;color:#a9a9b0;background-color:transparent;border:none;cursor:pointer;transition:all .2s ease-in-out;border-bottom:3px solid transparent}.tab-button:hover{background-color:#282c34;color:#fff}.tab-button.active{color:#61dafb;border-bottom:3px solid #61dafb}.content-panel{flex-grow:1;overflow:auto;text-align:left}.content-panel::-webkit-scrollbar{width:12px;height:12px}.content-panel::-webkit-scrollbar-track{background:#282c34}.content-panel::-webkit-scrollbar-thumb{background-color:#4f535d;border-radius:20px;border:3px solid #282c34}
