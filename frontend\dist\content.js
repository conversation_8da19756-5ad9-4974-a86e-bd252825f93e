// This script runs on the webpage and listens for messages from the extension's popup.
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // Check if the message is the one we're expecting.
  if (request.action === "getHTML") {
    // Respond with the full HTML of the page.
    sendResponse({ html: document.documentElement.outerHTML });
  }
  // 'return true' is necessary to indicate that you will be sending a response asynchronously.
  return true;
});