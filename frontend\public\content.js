/**
 * Content Script for Advanced HTML Extractor
 * Extracts HTML content from the current page and communicates with popup
 */

class HTMLExtractor {
  constructor() {
    this.isReady = false;
    this.init();
  }

  init() {
    // Wait for DOM to be fully loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.isReady = true;
      });
    } else {
      this.isReady = true;
    }

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async response
    });
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'extractHTML':
          const htmlData = await this.extractHTML(request.options);
          sendResponse({ success: true, data: htmlData });
          break;
        
        case 'extractElement':
          const elementData = await this.extractElement(request.selector);
          sendResponse({ success: true, data: elementData });
          break;
        
        case 'getPageInfo':
          const pageInfo = this.getPageInfo();
          sendResponse({ success: true, data: pageInfo });
          break;
        
        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Content script error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async extractHTML(options = {}) {
    const {
      includeDoctype = true,
      includeComments = false,
      minifyWhitespace = false,
      extractType = 'full' // 'full', 'body', 'head', 'visible'
    } = options;

    let htmlContent = '';
    
    switch (extractType) {
      case 'full':
        htmlContent = document.documentElement.outerHTML;
        break;
      case 'body':
        htmlContent = document.body ? document.body.outerHTML : '';
        break;
      case 'head':
        htmlContent = document.head ? document.head.outerHTML : '';
        break;
      case 'visible':
        htmlContent = this.extractVisibleContent();
        break;
      default:
        htmlContent = document.documentElement.outerHTML;
    }

    // Add doctype if requested and extracting full document
    if (includeDoctype && extractType === 'full') {
      const doctype = this.getDoctype();
      htmlContent = doctype + '\n' + htmlContent;
    }

    // Remove comments if not requested
    if (!includeComments) {
      htmlContent = htmlContent.replace(/<!--[\s\S]*?-->/g, '');
    }

    // Minify whitespace if requested
    if (minifyWhitespace) {
      htmlContent = this.minifyHTML(htmlContent);
    }

    return {
      html: htmlContent,
      stats: this.getHTMLStats(htmlContent),
      pageInfo: this.getPageInfo()
    };
  }

  extractElement(selector) {
    try {
      const element = document.querySelector(selector);
      if (!element) {
        throw new Error(`Element not found: ${selector}`);
      }

      return {
        html: element.outerHTML,
        stats: this.getHTMLStats(element.outerHTML),
        selector: selector,
        tagName: element.tagName.toLowerCase()
      };
    } catch (error) {
      throw new Error(`Failed to extract element: ${error.message}`);
    }
  }

  extractVisibleContent() {
    // Get all visible elements
    const visibleElements = Array.from(document.body.querySelectorAll('*'))
      .filter(el => {
        const style = window.getComputedStyle(el);
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               style.opacity !== '0' &&
               el.offsetWidth > 0 && 
               el.offsetHeight > 0;
      });

    // Create a simplified DOM structure with only visible elements
    const tempDiv = document.createElement('div');
    visibleElements.forEach(el => {
      if (!tempDiv.contains(el)) {
        tempDiv.appendChild(el.cloneNode(true));
      }
    });

    return tempDiv.innerHTML;
  }

  getDoctype() {
    const doctype = document.doctype;
    if (!doctype) return '';
    
    return `<!DOCTYPE ${doctype.name}${doctype.publicId ? ` PUBLIC "${doctype.publicId}"` : ''}${doctype.systemId ? ` "${doctype.systemId}"` : ''}>`;
  }

  minifyHTML(html) {
    return html
      .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
      .replace(/>\s+</g, '><') // Remove whitespace between tags
      .trim();
  }

  getHTMLStats(html) {
    const lines = html.split('\n').length;
    const characters = html.length;
    const words = html.split(/\s+/).filter(word => word.length > 0).length;
    
    // Count different types of elements
    const tags = html.match(/<[^>]+>/g) || [];
    const elements = tags.filter(tag => !tag.startsWith('</')).length;
    const comments = (html.match(/<!--[\s\S]*?-->/g) || []).length;
    
    return {
      lines,
      characters,
      words,
      elements,
      comments,
      size: new Blob([html]).size // Size in bytes
    };
  }

  getPageInfo() {
    return {
      title: document.title,
      url: window.location.href,
      domain: window.location.hostname,
      protocol: window.location.protocol,
      lastModified: document.lastModified,
      charset: document.characterSet,
      readyState: document.readyState,
      timestamp: new Date().toISOString()
    };
  }
}

// Initialize the HTML extractor
new HTMLExtractor();

console.log('Advanced HTML Extractor content script loaded');
