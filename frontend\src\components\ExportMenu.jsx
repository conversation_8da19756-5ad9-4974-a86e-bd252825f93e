import React, { useState } from 'react';
import { 
  Download, 
  Copy, 
  FileText, 
  Code2, 
  Image,
  Share2,
  ChevronDown,
  Check
} from 'lucide-react';

const ExportMenu = ({ htmlContent, formattedHtml, pageInfo, onSuccess, onError }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(null);

  const copyToClipboard = async (content, type) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(type);
      setTimeout(() => setCopied(null), 2000);
      onSuccess(`${type} copied to clipboard!`);
    } catch (error) {
      onError('Failed to copy to clipboard');
    }
  };

  const downloadFile = (content, filename, mimeType = 'text/html') => {
    try {
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      onSuccess(`${filename} downloaded successfully!`);
    } catch (error) {
      onError('Failed to download file');
    }
  };

  const generateFilename = (extension) => {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const domain = pageInfo?.domain || 'webpage';
    return `${domain}-${timestamp}.${extension}`;
  };

  const exportAsHTML = () => {
    downloadFile(formattedHtml, generateFilename('html'), 'text/html');
  };

  const exportAsText = () => {
    // Strip HTML tags to get plain text
    const textContent = formattedHtml.replace(/<[^>]*>/g, '').trim();
    downloadFile(textContent, generateFilename('txt'), 'text/plain');
  };

  const exportAsJSON = () => {
    const data = {
      pageInfo,
      extractedAt: new Date().toISOString(),
      htmlContent: formattedHtml,
      rawContent: htmlContent,
      metadata: {
        userAgent: navigator.userAgent,
        extractorVersion: '1.0.0'
      }
    };
    downloadFile(
      JSON.stringify(data, null, 2), 
      generateFilename('json'), 
      'application/json'
    );
  };

  const shareHTML = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `HTML from ${pageInfo?.title || 'webpage'}`,
          text: formattedHtml.substring(0, 200) + '...',
          url: pageInfo?.url
        });
        onSuccess('Shared successfully!');
      } catch (error) {
        if (error.name !== 'AbortError') {
          onError('Failed to share');
        }
      }
    } else {
      // Fallback to copying URL
      await copyToClipboard(pageInfo?.url || '', 'URL');
    }
  };

  const exportOptions = [
    {
      icon: FileText,
      label: 'Download HTML',
      action: exportAsHTML,
      description: 'Save as .html file'
    },
    {
      icon: Code2,
      label: 'Download JSON',
      action: exportAsJSON,
      description: 'Save with metadata as .json'
    },
    {
      icon: FileText,
      label: 'Download Text',
      action: exportAsText,
      description: 'Save as plain text .txt'
    },
    {
      icon: Copy,
      label: 'Copy HTML',
      action: () => copyToClipboard(formattedHtml, 'HTML'),
      description: 'Copy formatted HTML',
      copied: copied === 'HTML'
    },
    {
      icon: Copy,
      label: 'Copy Raw',
      action: () => copyToClipboard(htmlContent, 'Raw HTML'),
      description: 'Copy unformatted HTML',
      copied: copied === 'Raw HTML'
    },
    {
      icon: Share2,
      label: 'Share',
      action: shareHTML,
      description: 'Share page info'
    }
  ];

  return (
    <div className="export-menu">
      <button
        className="export-trigger"
        onClick={() => setIsOpen(!isOpen)}
        disabled={!formattedHtml}
      >
        <Download size={16} />
        Export
        <ChevronDown 
          size={14} 
          className={`chevron ${isOpen ? 'open' : ''}`}
        />
      </button>

      {isOpen && (
        <div className="export-dropdown">
          {exportOptions.map((option, index) => (
            <button
              key={index}
              className="export-option"
              onClick={() => {
                option.action();
                setIsOpen(false);
              }}
            >
              <div className="option-icon">
                {option.copied ? (
                  <Check size={16} className="check-icon" />
                ) : (
                  <option.icon size={16} />
                )}
              </div>
              <div className="option-content">
                <div className="option-label">{option.label}</div>
                <div className="option-description">{option.description}</div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ExportMenu;
