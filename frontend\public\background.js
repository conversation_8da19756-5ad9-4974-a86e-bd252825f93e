/**
 * Background Script for Advanced HTML Extractor
 * Handles extension lifecycle, storage, and communication between components
 */

class ExtensionBackground {
  constructor() {
    this.init();
  }

  init() {
    // Handle extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details);
    });

    // Handle messages from popup and content scripts
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Handle tab updates to refresh content script if needed
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });

    console.log('Advanced HTML Extractor background script initialized');
  }

  async handleInstallation(details) {
    try {
      if (details.reason === 'install') {
        // Set default settings on first install
        await this.setDefaultSettings();
        console.log('Extension installed with default settings');
      } else if (details.reason === 'update') {
        // Handle extension updates
        console.log('Extension updated to version', chrome.runtime.getManifest().version);
      }
    } catch (error) {
      console.error('Installation handler error:', error);
    }
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'extractFromActiveTab':
          const result = await this.extractFromActiveTab(request.options);
          sendResponse({ success: true, data: result });
          break;

        case 'saveSettings':
          await this.saveSettings(request.settings);
          sendResponse({ success: true });
          break;

        case 'getSettings':
          const settings = await this.getSettings();
          sendResponse({ success: true, data: settings });
          break;

        case 'saveExtractedHTML':
          await this.saveExtractedHTML(request.data);
          sendResponse({ success: true });
          break;

        case 'getHistory':
          const history = await this.getHistory();
          sendResponse({ success: true, data: history });
          break;

        case 'clearHistory':
          await this.clearHistory();
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Background message handler error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async extractFromActiveTab(options = {}) {
    try {
      // Get the active tab
      const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!activeTab) {
        throw new Error('No active tab found');
      }

      // Inject content script if not already present
      await this.ensureContentScript(activeTab.id);

      // Send extraction request to content script
      const response = await chrome.tabs.sendMessage(activeTab.id, {
        action: 'extractHTML',
        options: options
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to extract HTML');
      }

      // Save to history if enabled
      const settings = await this.getSettings();
      if (settings.saveToHistory) {
        await this.saveToHistory(response.data, activeTab);
      }

      return response.data;
    } catch (error) {
      console.error('Extract from active tab error:', error);
      throw error;
    }
  }

  async ensureContentScript(tabId) {
    try {
      // Try to ping the content script
      await chrome.tabs.sendMessage(tabId, { action: 'ping' });
    } catch (error) {
      // Content script not present, inject it
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content.js']
      });
    }
  }

  async handleTabUpdate(tabId, changeInfo, tab) {
    // Re-inject content script when page is reloaded
    if (changeInfo.status === 'complete' && tab.url && !tab.url.startsWith('chrome://')) {
      try {
        await this.ensureContentScript(tabId);
      } catch (error) {
        console.log('Could not inject content script into tab:', tab.url);
      }
    }
  }

  async setDefaultSettings() {
    const defaultSettings = {
      includeDoctype: true,
      includeComments: false,
      minifyWhitespace: false,
      extractType: 'full',
      autoFormat: true,
      saveToHistory: true,
      maxHistoryItems: 50,
      theme: 'dark',
      fontSize: 14,
      wordWrap: true
    };

    await chrome.storage.sync.set({ settings: defaultSettings });
  }

  async saveSettings(settings) {
    await chrome.storage.sync.set({ settings: settings });
  }

  async getSettings() {
    const result = await chrome.storage.sync.get('settings');
    return result.settings || {};
  }

  async saveExtractedHTML(data) {
    const timestamp = new Date().toISOString();
    const extractedData = {
      ...data,
      timestamp: timestamp,
      id: this.generateId()
    };

    await chrome.storage.local.set({
      [`extracted_${extractedData.id}`]: extractedData
    });
  }

  async saveToHistory(htmlData, tab) {
    try {
      const settings = await this.getSettings();
      const maxItems = settings.maxHistoryItems || 50;

      // Get current history
      const result = await chrome.storage.local.get('history');
      let history = result.history || [];

      // Add new item
      const historyItem = {
        id: this.generateId(),
        timestamp: new Date().toISOString(),
        title: tab.title,
        url: tab.url,
        domain: new URL(tab.url).hostname,
        stats: htmlData.stats,
        preview: this.generatePreview(htmlData.html)
      };

      history.unshift(historyItem);

      // Limit history size
      if (history.length > maxItems) {
        history = history.slice(0, maxItems);
      }

      await chrome.storage.local.set({ history: history });
    } catch (error) {
      console.error('Save to history error:', error);
    }
  }

  async getHistory() {
    const result = await chrome.storage.local.get('history');
    return result.history || [];
  }

  async clearHistory() {
    await chrome.storage.local.remove('history');
  }

  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  generatePreview(html, maxLength = 200) {
    // Remove HTML tags and get text content
    const textContent = html.replace(/<[^>]*>/g, '').trim();
    return textContent.length > maxLength 
      ? textContent.substring(0, maxLength) + '...'
      : textContent;
  }
}

// Initialize the background script
new ExtensionBackground();
