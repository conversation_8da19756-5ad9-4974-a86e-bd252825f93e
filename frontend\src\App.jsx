import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  Code, 
  Eye,
  FileText,
  Zap,
  RefreshCw,  
  CheckCircle,
  AlertCircle,
  Minimize2,
  Maximize2
} from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus, vs } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { HTMLFormatter } from './utils/htmlFormatter';
import ExportMenu from './components/ExportMenu';
import './App.css';

const App = () => {
  // State management
  const [htmlContent, setHtmlContent] = useState('');
  const [formattedHtml, setFormattedHtml] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [pageInfo, setPageInfo] = useState(null);
  const [stats, setStats] = useState(null);
  const [activeTab, setActiveTab] = useState('formatted');
  const [isCompact, setIsCompact] = useState(false);

  // Settings state
  const [settings, setSettings] = useState({
    includeDoctype: true,
    includeComments: false,
    minifyWhitespace: false,
    extractType: 'full',
    autoFormat: true,
    theme: 'dark',
    fontSize: 14,
    wordWrap: true
  });

  // Refs
  const htmlFormatter = useRef(new HTMLFormatter());
  const textareaRef = useRef(null);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, []);

  // Auto-extract HTML when component mounts
  useEffect(() => {
    extractHTML();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
      if (response.success) {
        setSettings(prev => ({ ...prev, ...response.data }));
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const saveSettings = async (newSettings) => {
    try {
      await chrome.runtime.sendMessage({
        action: 'saveSettings',
        settings: newSettings
      });
      setSettings(newSettings);
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  };

  const extractHTML = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'extractFromActiveTab',
        options: {
          includeDoctype: settings.includeDoctype,
          includeComments: settings.includeComments,
          minifyWhitespace: settings.minifyWhitespace,
          extractType: settings.extractType
        }
      });

      if (response.success) {
        const { html, stats: htmlStats, pageInfo: info } = response.data;
        setHtmlContent(html);
        setStats(htmlStats);
        setPageInfo(info);

        // Format HTML if auto-format is enabled
        if (settings.autoFormat) {
          const formatted = htmlFormatter.current.format(html);
          setFormattedHtml(formatted);
        } else {
          setFormattedHtml(html);
        }

        setSuccess('HTML extracted successfully!');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error(response.error || 'Failed to extract HTML');
      }
    } catch (error) {
      setError(error.message);
      console.error('Extract HTML error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatHTML = () => {
    if (!htmlContent) return;

    try {
      const formatted = htmlFormatter.current.format(htmlContent);
      setFormattedHtml(formatted);
      setSuccess('HTML formatted successfully!');
      setTimeout(() => setSuccess(null), 2000);
    } catch (error) {
      setError('Failed to format HTML: ' + error.message);
    }
  };

  const copyToClipboard = async (content = formattedHtml) => {
    try {
      await navigator.clipboard.writeText(content);
      setSuccess('Copied to clipboard!');
      setTimeout(() => setSuccess(null), 2000);
    } catch (error) {
      setError('Failed to copy to clipboard');
    }
  };



  const handleExtractTypeChange = (type) => {
    const newSettings = { ...settings, extractType: type };
    saveSettings(newSettings);
    extractHTML();
  };

  return (
    <div className={`app ${settings.theme} ${isCompact ? 'compact' : ''}`}>
      {/* Header */}
      <div className="header">
        <div className="header-left">
          <Code className="logo" size={20} />
          <h1>HTML Extractor</h1>
        </div>
        <div className="header-right">
          <button
            className="icon-button"
            onClick={() => setIsCompact(!isCompact)}
            title={isCompact ? 'Expand' : 'Compact'}
          >
            {isCompact ? <Maximize2 size={16} /> : <Minimize2 size={16} />}
          </button>
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="message error">
          <AlertCircle size={16} />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="message success">
          <CheckCircle size={16} />
          <span>{success}</span>
        </div>
      )}

      {/* Page Info */}
      {pageInfo && !isCompact && (
        <div className="page-info">
          <div className="info-item">
            <span className="label">Title:</span>
            <span className="value" title={pageInfo.title}>
              {pageInfo.title.length > 40 ? pageInfo.title.substring(0, 40) + '...' : pageInfo.title}
            </span>
          </div>
          <div className="info-item">
            <span className="label">Domain:</span>
            <span className="value">{pageInfo.domain}</span>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="controls">
        <div className="control-group">
          <button
            className="button primary"
            onClick={extractHTML}
            disabled={isLoading}
          >
            {isLoading ? <RefreshCw className="spinning" size={16} /> : <Zap size={16} />}
            {isLoading ? 'Extracting...' : 'Extract'}
          </button>

          <select
            value={settings.extractType}
            onChange={(e) => handleExtractTypeChange(e.target.value)}
            className="select"
          >
            <option value="full">Full Page</option>
            <option value="body">Body Only</option>
            <option value="head">Head Only</option>
            <option value="visible">Visible Content</option>
          </select>
        </div>

        <div className="control-group">
          <button
            className="button secondary"
            onClick={formatHTML}
            disabled={!htmlContent}
            title="Format HTML"
          >
            <FileText size={16} />
          </button>

          <button
            className="button secondary"
            onClick={() => copyToClipboard()}
            disabled={!formattedHtml}
            title="Copy to Clipboard"
          >
            <Copy size={16} />
          </button>

          <ExportMenu
            htmlContent={htmlContent}
            formattedHtml={formattedHtml}
            pageInfo={pageInfo}
            onSuccess={(message) => {
              setSuccess(message);
              setTimeout(() => setSuccess(null), 2000);
            }}
            onError={(message) => {
              setError(message);
              setTimeout(() => setError(null), 3000);
            }}
          />
        </div>
      </div>

      {/* Stats */}
      {stats && !isCompact && (
        <div className="stats">
          <div className="stat-item">
            <span className="stat-value">{stats.lines}</span>
            <span className="stat-label">Lines</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{stats.elements}</span>
            <span className="stat-label">Elements</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{(stats.size / 1024).toFixed(1)}KB</span>
            <span className="stat-label">Size</span>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="tabs">
        <button
          className={`tab ${activeTab === 'formatted' ? 'active' : ''}`}
          onClick={() => setActiveTab('formatted')}
        >
          <Eye size={16} />
          Formatted
        </button>
        <button
          className={`tab ${activeTab === 'raw' ? 'active' : ''}`}
          onClick={() => setActiveTab('raw')}
        >
          <Code size={16} />
          Raw
        </button>
      </div>

      {/* Content Display */}
      <div className="content">
        {activeTab === 'formatted' ? (
          <SyntaxHighlighter
            language="html"
            style={settings.theme === 'dark' ? vscDarkPlus : vs}
            customStyle={{
              margin: 0,
              fontSize: `${settings.fontSize}px`,
              maxHeight: isCompact ? '200px' : '400px',
              overflow: 'auto'
            }}
            wrapLines={settings.wordWrap}
            showLineNumbers={true}
          >
            {formattedHtml || 'No HTML content to display'}
          </SyntaxHighlighter>
        ) : (
          <textarea
            ref={textareaRef}
            value={htmlContent}
            onChange={(e) => setHtmlContent(e.target.value)}
            className="raw-textarea"
            style={{
              fontSize: `${settings.fontSize}px`,
              height: isCompact ? '200px' : '400px'
            }}
            placeholder="Raw HTML content will appear here..."
          />
        )}
      </div>
    </div>
  );
};

export default App;